// Chrome 扩展消息通信工具函数

import { BackgroundMessageType, ConnectionMessageType } from "@/types/network"
import type { Message, Response, FilteredRequestInfo } from "@/types/network"

/**
 * 发送消息到background script
 */
export function sendMessageToBackground(message: Message): Promise<Response> {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage(message, (response: Response) => {
      if (chrome.runtime.lastError) {
        console.error("发送消息失败:", chrome.runtime.lastError)
        resolve({
          success: false,
          error: chrome.runtime.lastError.message
        })
      } else {
        resolve(response)
      }
    })
  })
}

/**
 * 获取当前标签页的过滤请求列表
 */
export async function getFilteredRequests(tabId?: number): Promise<FilteredRequestInfo[]> {
  try {
    const message: Message = {
      type: BackgroundMessageType.GET_FILTERED_REQUESTS,
      tabId
    }

    const response = await sendMessageToBackground(message)

    if (response.success && response.data?.requests) {
      return response.data.requests
    }

    console.warn("获取过滤请求失败:", response.error)
    return []
  } catch (error) {
    console.error("获取过滤请求异常:", error)
    return []
  }
}

/**
 * 获取当前活动标签页ID
 */
export async function getCurrentTabId(): Promise<number | undefined> {
  try {
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
    return tabs[0]?.id
  } catch (error) {
    console.error("获取当前标签页ID失败:", error)
    return undefined
  }
}

/**
 * 清空过滤请求列表
 */
export async function clearFilteredRequests(): Promise<boolean> {
  try {
    const message: Message = {
      type: BackgroundMessageType.CLEAR_FILTERED_REQUESTS
    }

    const response = await sendMessageToBackground(message)
    return response.success
  } catch (error) {
    console.error("清空过滤请求失败:", error)
    return false
  }
}

/**
 * 清空指定标签页的过滤请求列表
 */
export async function clearFilteredRequestsByTab(tabId: number): Promise<boolean> {
  try {
    const message: Message = {
      type: BackgroundMessageType.CLEAR_FILTERED_REQUESTS_BY_TAB,
      tabId
    }

    const response = await sendMessageToBackground(message)
    return response.success
  } catch (error) {
    console.error("清空标签页过滤请求失败:", error)
    return false
  }
}

/**
 * 监听background数据更新
 */
export function listenToBackgroundUpdates(callback: () => void): () => void {
  // 监听来自background的消息
  const messageListener = (message: any) => {
    if (message.type === ConnectionMessageType.DATA_UPDATE || message.type === "INITIAL_DATA") {
      callback()
    }
  }

  // 监听标签页激活事件
  const tabActivatedListener = () => {
    callback()
  }

  // 添加监听器
  chrome.runtime.onMessage.addListener(messageListener)
  if (chrome.tabs && chrome.tabs.onActivated) {
    chrome.tabs.onActivated.addListener(tabActivatedListener)
  }

  // 返回清理函数
  return () => {
    chrome.runtime.onMessage.removeListener(messageListener)
    if (chrome.tabs && chrome.tabs.onActivated) {
      chrome.tabs.onActivated.removeListener(tabActivatedListener)
    }
  }
}

/**
 * 建立与background的持久连接（用于popup）
 */
export function connectToBackground(): chrome.runtime.Port | null {
  try {
    const port = chrome.runtime.connect({ name: "popup" })

    port.onMessage.addListener((message) => {
      console.log("收到background消息:", message)
    })

    port.onDisconnect.addListener(() => {
      console.log("与background连接断开")
    })

    return port
  } catch (error) {
    console.error("连接background失败:", error)
    return null
  }
}

/**
 * 建立与background的持久连接（用于sidepanel）
 */
export function connectToBackgroundAsSidepanel(): chrome.runtime.Port | null {
  try {
    const port = chrome.runtime.connect({ name: "sidepanel" })

    port.onMessage.addListener((message) => {
      console.log("SidePanel收到background消息:", message)
    })

    port.onDisconnect.addListener(() => {
      console.log("SidePanel与background连接断开")
    })

    return port
  } catch (error) {
    console.error("SidePanel连接background失败:", error)
    return null
  }
}

/**
 * 设置请求头
 */
export async function setRequestHeaders(requestId: string, url: string, requestHeaders?: chrome.webRequest.HttpHeader[], pageUrl?: string): Promise<boolean> {
  try {
    // 获取当前活动标签页ID（用于侧边栏等非标签页上下文）
    const currentTabId = await getCurrentTabId()

    const message: Message = {
      type: BackgroundMessageType.SET_REQUEST_HEADERS,
      payload: {
        requestId,
        url,
        requestHeaders,
        pageUrl,
        tabId: currentTabId // 直接在payload中包含tabId
      }
    }

    const response = await sendMessageToBackground(message)
    return response.success
  } catch (error) {
    console.error("设置请求头失败:", error)
    return false
  }
}





/**
 * 切换侧边栏显示状态
 */
export async function toggleSidepanel(): Promise<boolean> {
  try {
    const message: Message = {
      type: BackgroundMessageType.TOGGLE_SIDEPANEL
    }

    const response = await sendMessageToBackground(message)
    return response.success
  } catch (error) {
    console.error("切换侧边栏失败:", error)
    return false
  }
}